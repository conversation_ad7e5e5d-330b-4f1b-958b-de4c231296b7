import pygame
import sys
import math
import random

# Initialize pygame
pygame.init()

# Screen dimensions
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Bouncing Balls in Spinning Hexagon")

# Colors
BACKGROUND = (20, 20, 30)
HEXAGON_COLOR = (100, 200, 255)
HEXAGON_BORDER = (50, 150, 255)
BALL_COLORS = [
    (255, 100, 100), (100, 255, 100), (100, 100, 255),
    (255, 200, 100), (200, 100, 255), (100, 255, 200)
]
TEXT_COLOR = (220, 220, 220)
UI_BACKGROUND = (30, 30, 40, 200)
UI_BORDER = (60, 60, 80)

# Physics constants
GRAVITY = 0.5
FRICTION = 0.99
ELASTICITY = 0.8
MIN_ROTATION_SPEED = 0.5
MAX_ROTATION_SPEED = 5.0

class Ball:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.radius = random.randint(12, 18)
        self.mass = self.radius * 0.5
        self.vx = random.uniform(-3, 3)
        self.vy = random.uniform(-3, 3)
        self.color = random.choice(BALL_COLORS)
        self.trail = []
        =  =  = 10
        
    def update(self):
        # Apply gravity
        self.vy += GRAVITY
        
        # Apply friction
        self.vx *= FRICTION
        self.vy *= FRICTION
        
        # Update position
        self.x += self.vx
        self.y += self.vy
        
        # Add current position to trail
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.max_trail:
            self.trail.pop(0)
    
    def draw(self, surface):
        # Draw trail
        for i, (trail_x, trail_y) in enumerate(self.trail):
            alpha = int(255 * (i / len(self.trail)))
            radius = int(self.radius * (i / len(self.trail)))
            pygame.draw.circle(surface, (*self.color, alpha), 
                              (int(trail_x), int(trail_y)), radius)
        
        # Draw ball
        pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), self.radius)
        pygame.draw.circle(surface, (255, 255, 255), (int(self.x), int(self.y)), 
                          self.radius, 1)
        
        # Draw highlight
        pygame.draw.circle(surface, (255, 255, 255, 100), 
                          (int(self.x - self.radius/3), int(self.y - self.radius/3)), 
                          self.radius//3)

class Hexagon:
    def __init__(self, center_x, center_y, radius):
        self.center_x = center_x
        self.center_y = center_y
        self.radius = radius
        self.angle = 0
        self.rotation_speed = 2.0
        self.rotation_direction = 1
        self.rotation_paused = False
        self.vertices = []
        self.calculate_vertices()
        
    def calculate_vertices(self):
        self.vertices = []
        for i in range(6):
            angle_rad = self.angle + math.radians(i * 60)
            x = self.center_x + self.radius * math.cos(angle_rad)
            y = self.center_y + self.radius * math.sin(angle_rad)
            self.vertices.append((x, y))
    
    def update(self):
        if not self.rotation_paused:
            self.angle += self.rotation_speed * 0.01 * self.rotation_direction
            self.calculate_vertices()
    
    def draw(self, surface):
        # Draw hexagon fill
        pygame.draw.polygon(surface, HEXAGON_COLOR, self.vertices)
        
        # Draw hexagon border
        pygame.draw.polygon(surface, HEXAGON_BORDER, self.vertices, 3)
        
        # Draw center marker
        pygame.draw.circle(surface, (255, 255, 255), (int(self.center_x), int(self.center_y)), 5)
        
        # Draw rotation direction indicator
        indicator_angle = self.angle + math.radians(90)
        indicator_x = self.center_x + 20 * mathicator_icator_angle)
        indicator_y = self.center_y + 20 * math.sin(indicator_angle)
        pygame.draw.line(surface, (255, 255, 0), 
                        (self.center_x, self.center_y), 
                        (indicator_x, indicator_y), 3)

def distance(x1, y1, x2, y2):
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def check_ball_collision(ball1, ball2):
    dx = ball2.x - ball1.x
    dy = ball2.y - ball1.y
    dist = math.sqrt(dx*dx + dy*dy)
    
    if dist < ball1.radius + ball2.radius:
        # Collision response
        angle = math.atan2(dy, dx)
        
        # Calculate components of velocity vectors  
        v1 = math.sqrt(ball1.vx**2 + ball1.vy**2)
        v2 = math.sqrt(ball2.vx**2 + ball2.vy**2)
        
        dir1 = math.atan2(ball1.vy, ball1.vx)
        dir2 = math.atan2(ball2.vy, ball2.vx)
        
        # Calculate new velocities
        new_vx1 = v1 * math.cos(dir1 - angle) * ELASTICITY
        new_vy1 = v1 * math.sin(dir1 - angle) * ELASTICITY
        new_vx2 = v2 * math.cos(dir2 - angle) * ELASTICITY
        new_vy2 = v2 * math.sin(dir2 - angle) * ELASTICITY
        
        # Final velocities after collision
        ball1.vx = math.cos(angle) * new_vx1 + math.cos(angle + math.pi/2) * new_vy1
        ball1.vy = math.sin(angle) * new_vx1 + math.sin(angle + math.pi/2) * new_vy        ball        ball2.vx = math.cos(angle) * new_vx2 + math.cos(angle + math.pi/2) * new_vy2
        ball2.vy = math.sin(angle) * new(angle(angle(angle(angle + math.pi/2) * new_vy2
        
        # Prevent balls from sticking together
        overlap = (ball1.radius + ball2.radius - dist) / 2
        ball1.x -= overlap * math.cos(angle)
        ball1.y -= overlap * math.sin(angle)
        ball2.x += overlap * math.cos(angle)
        ball2.y += overlap * math.sin(angle)

def check_wall_collision(ball, hexagon):
    # Convert ball position to hexagon's local coordinate system
    rel_x = ball.x - hexagon.center_x
    rel_y = ball.y - hexagon.center_y
    
    # Rotate ball position to align with hexagon's orientation
    rotated_x = rel_x * math.cos(-hexagon.angle) - rel_y * math.sin(-hexagon.angle)
    rotated_y = rel_x * math.sin(-hexagon.angle) + rel_y * math.cos(-hexagon.angle)
    
    # Calculate distance from center
    dist = math.sqrt(rotated_x**2 + rotated_y**2)
    
    # If ball is outside hexagon
    if dist > hexagon.radius - ball.radius:
        # Calculate angle
        angle = math.atan2(rotated_y, rotated_x)
        
        # Find closest hexagon side
        side_angle = round(angle / (math.pi/3)) * (math.pi/3)
        
        # Calculate collision point on hexagon
        collision_x = hexagon.center_x + (hexagon.radius - ball.radius) * math.cos(side_angle + hexagon.angle)
        collision_y = hexagon.center_y + (hexagon.radius - ball.radius) * math.sin(side_angle + hexagon.angle)
        
        # Calculate normal vector (perpendicular to the wall)
        normal_x = ball.x - collision_x
        normal_y = ball.y - collision_y
        normal_length = math.sqrt(normal_x**2 + normal_y**2)
        normal_x /= normal_length
        normal_y /= normal_length
        
        # Reflect velocity vector
        dot_product = ball.vx * normal_x + ball.vy * normal_y
        ball.vx = ELASTICITY * (ball.vx - 2 * dot_product * normal_x)
        ball.vy = ELASTICITY * (ball.vy - 2 * dot_product * normal_y)
        
        # Add rotational effect based on hexagon's rotation
        tangent_x = -normal_y
        tangent_y = normal_x
        ball.vx += tangent_x * hexagon.rotation_speed * 0.2 * hexagon.rotation_direction
        ball.vy += tangent_y * hexagon.rotation_speed * 0.2 * hexagon.rotation_direction
        
        # Position correction
        ball.x = collision_x + normal_x * ball.radius
        ball.y = collision_y + normal_y * ball.radius

def draw_ui(surface, hexagon, balls):
    # Draw controls panel
    pygame.draw.rect(surface, UI_BACKGROUND, (10, 10, 300, 130), 0, 10)
    pygame.draw.rect(surface, UI_BORDER, (10, 10,  130 130), 2, 10)
    
    # Draw title
    title_font = pygame.font.SysFont('Arial', 24, bold=True)
    title = title_font.render("CONTROLS", True, (200, 200, 255))
    surface.blit(title, (20, 20))
    
    # Draw controls
    font = pygame.font.SysFont('Arial', 16)
    controls = [
        "Left Click: Add Ball",
        "Right Click: Remove Ball",
        "Up/Down: Adjust Rotation Speed",
        "R: Reverse Rotation",
        "Space: Pause/Resume Rotation"
    ]
    
    for i, text in enumerate(controls):
        text_surface = font.render(text, True, TEXT_COLOR)
        surface.blit(text_surface, (20, 50 + i*20))
    
    # Draw stats panel
    pygame.draw.rect(surface, UI_BACKGROUND, (10, HEIGHT - 80, 250, 70), 0, 10)
    pygame.draw.rect(surface, UI_BORDER, ( -  -  - 80, 250, 70), 2, 10)
    
    # Draw stats
    stats = [
        f"Rotation Speed: {abs(hexagon * hex * hex * hexagon.rotation_direction):.1f}",
        f"Rotation Direction: {'CW' if hexagon.rotation_direction > 0 else 'CCW'}",
        f"Balls: {len(balls)}",
        f"Status: {'PAUSED' if hexagon.rotation_paused else 'RUNNING'}"
    ]
    
    for i, text in enumerate(stats):
        text_surface = font.render(text, True, TEXT_COLOR)
        surface.blit(text_surface, (20, HEIGHT - 70 + i*15))

def main():
    clock = pygame.time.Clock()
    
    # Create hexagon
    hexagon = Hexagon(WIDTH//2, HEIGHT//2, 200)
    
    # Create balls
    balls = []
    for _ in range(3):
        angle = random.uniform(0, 2*math.pi)
        distance = random.uniform(50, hexagon.radius - 30)
        x = hexagon.center_x + distance * math.cos(angle)
        y = hexagon.center_y + distance * math.sin(angle)
        balls.append(Ball(x, y))
    
    # Create a surface for trail effects with alpha
    trail_surface = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    balls.append(Ball(event.pos[0], event.pos[1]))
                elif event.button == 3:  # Right click
                    if balls:
                        # Find closest ball to mouse
                        mouse_x, mouse_y = event.pos
                        closest_ball = min(balls, key=lambda b: distance(b.x, b.y, mouse_x, mouse_y))
                        balls.remove(closest_ball)
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    hexagon.rotation_speed = min(hexagon.rotation_speed + 0.5, MAX_ROTATION_SPEED)
                elif event.key == pygame.K_DOWN:
                    hexagon.rotation_speed = max(hexagon.rotation_speed - 0.5, MIN_ROTATION_SPEED)
                elif event.key == pygame.K_r:
                    hexagon.rotation_direction *= -1
                elif event.key == pygame.K_SPACE:
                    hexagon.rotation_paused = not hexagon.rotation_paused
        
        # Update hexagon
        hexagon.update()
        
        # Update balls
        for ball in balls:
            ball.update()
        
        # Check collisions
        for i in range(len(balls)):
            for j in range(i+1, len(balls)):
                check_ball_collision(balls[i], balls[j])
        
        for ball in balls:
            check_wall_collision(ball, hexagon)
        
        # Drawing
        screen.fill(BACKGROUND)
        
        # Draw starry background
        for _ in range(100):
            x = random.randint(0, WIDTH)
            y = random.randint(0, HEIGHT)
            size = random.randint(1, 2)
            brightness = random.randint(100, 200)
            pygame.draw.circle(screen, (brightness, brightness, brightness), (x, y), size)
        
        # Draw hexagon
        hexagon.draw(screen)
        
        # Draw balls with trails
        trail_surface.fill((0, 0, 0, 0))
        for ball in balls:
            ball.draw(trail_surface)
        screen.blit(trail_surface, (0, 0))
        
        # Draw UI
        draw_ui(screen, hexagon, balls)
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()